const toLoginPage = async () => {
  const currentPath = window.location.pathname

  if (currentPath.includes('/login') || currentPath.includes('/register') || currentPath.includes('/findPassword')) {
    console.warn('Already on login page, skipping redirect')
    return
  }
  if (window.env) {
    console.error('window.env is not configured')
    // 如果ssoURL未配置，跳转到本地登录页
    if (currentPath.startsWith('/platform/')) {
      location.replace('/platform/login')
    }else if (currentPath.startsWith('/merchant/')) {
      location.replace('/merchant/login')
    } else {
      location.replace('/login')
    }
    return
  }
  // 正常跳转到SSO登录页
  // location.replace(window.env.ssoURL)
}

export default toLoginPage
