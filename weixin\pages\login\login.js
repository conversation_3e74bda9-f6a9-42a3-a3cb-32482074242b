import store from '../../utils/store.js'
import handleError from '../../utils/handleError.js'
import { setToken } from '../../utils/token.js'
import makeClient from '../../utils/makeClient.js'

const client = makeClient()

Page({
  data: {
    logoUrl: '',
    domainInfo: null,
    countdown: 0,
    phone: '',
    code: '',
    captcha: {
      url: '',
      answer: '',
      token: ''
    },
    otpToken: ''
  },

  async onLoad() {
    try {
      var domainInfo = this.getDomainInfo()

      const [err, r] = await client.domainInfo({
        body: {
          domain: domainInfo.domainName
        }
      })
      if (err) {
        handleError(err)
        return
      }
      this.setData({ domainInfo: r })
      store.setItem('domainInfo', r)
      domainInfo = r

      if (domainInfo && domainInfo.logoUrl) {
        const logoUrl = this.formatImageURL(domainInfo.logoUrl)
        this.setData({ logoUrl })
      }

      await this.refreshCaptcha()
    } catch (e) {
      console.error(e)
    }
  },
  async refreshCaptcha() {
    const domainInfo = this.getDomainInfo()

    const [err, r] = await client.createCaptcha()
    if (err) {
      handleError(err)
      return
    }

    const baseUrl = `https://${domainInfo.domainName}`
    const captchaUrl = `${baseUrl}/api/public/captcha?token=${encodeURIComponent(
      r
    )}`

    this.setData({
      'captcha.url': captchaUrl,
      'captcha.token': r
    })
  },
  onCaptchaInput(e) {
    this.setData({
      'captcha.answer': e.detail.value
    })
  },

  async getVerificationCode() {
    if (this.data.countdown > 0) {
      return
    }

    const [err, r] = await client.apiSendLoginSms({
      body: {
        receiver: this.data.phone,
        captchaToken: this.data.captcha.token,
        captchaAnswer: this.data.captcha.answer
      }
    })

    if (err) {
      handleError(err)
      this.refreshCaptcha()
      return
    }
    this.setData({
      countdown: 60,
      otpToken: r.token
    })

    const timer = setInterval(() => {
      if (this.data.countdown > 0) {
        this.setData({
          countdown: this.data.countdown - 1
        })
      } else {
        clearInterval(timer)
      }
    }, 1000)
  },

  getDomainInfo() {
    var domainInfo = store.getItem('domainInfo')
    if (!domainInfo) {
      domainInfo = {
        domainName: '156-dev.olading.com'
      }
    }
    return domainInfo
  },
  // 绑定输入框
  onPhoneInput(e) {
    this.setData({ phone: e.detail.value })
  },
  onCodeInput(e) {
    this.setData({ code: e.detail.value })
  },
  formatImageURL(id) {
    var domainInfo = store.getItem('domainInfo')
    const baseUrl = `https://${domainInfo.domainName}`

    return `${baseUrl}/api/public/previewFile/${id}`
  },

  async login() {
    const { phone, code, captcha, otpToken } = this.data
    if (!phone || !code || !captcha.answer) {
      handleError('请输入手机号和验证码')
      return
    }
    if (phone.length !== 11) {
      handleError('请输入正确的手机号')
      return
    }
    if (captcha.answer.length !== 4) {
      handleError('请输入正确的图形验证码')
      return
    }
    if (code.length !== 6) {
      handleError('请输入正确的验证码')
      return
    }
    if (!otpToken) {
      handleError('请先获取短信验证码')
      return
    }

    wx.showLoading({ title: '登录中...' })

    const [err, r] = await client.login({
      body: {
        account: phone,
        captchaAnswer: captcha.answer,
        captchaToken: captcha.token,
        otpToken: otpToken,
        code: code,
        smsLogin: true,
        type: 'PERSONAL'
      }
    })

    wx.hideLoading()
    if (err) {
      handleError(err)
      return
    }

    setToken(r.token)
    wx.reLaunch({ url: '/pages/index/index' })
  }
})
